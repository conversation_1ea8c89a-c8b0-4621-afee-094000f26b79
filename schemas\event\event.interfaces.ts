
export enum IStatusEvent {
  OPEN = 'OPEN',
  CLOSE = 'CLOSE',
}

export interface IPaymentInfoField {
  key: string;
  text: string;
  type?: string;
  isDefault?: boolean;
  isRequired?: boolean;
}

export interface IGeneral {
  collaboration?: 'eventista' | 'agency' | 'seatmap' | 'seatmap_zone';
  ticketType?: 'e_ticket' | 'hard_ticket';
  paymentInfoFields?: IPaymentInfoField[];
}

export interface IVisitor {
  name?: string;
  picture?: string;
  id?: string;
}

export type Category = 'concert' | 'live_show' | 'workshop' | 'fan_meeting';


export interface IEventCalendar {
  calendarId: string;
  startDate: Date;
  endDate: Date;
  maxTicketPerUser?: number;
}

export interface ITicketCalendar {
  thumbnail?: string;
  description?: string;
}

export interface IEvent {
  _id?: string;
  id?: string;
  eventId: string;
  eventName: string;
  location: string;
  address: string;
  pathName: string;
  thumbnail?: string;
  logo?: string;
  venueMap?: string;
  status?: IStatusEvent;
  eventCalendar: IEventCalendar[];
  ticketCalendar?: ITicketCalendar;
  hostedBy?: string;
  tickets: Record<string, any>[];
  eventDescriptions?: { title: string; description: string }[];
  maxTicketPerUser: number;
  note: string;
  general?: IGeneral;
  domain: string;
  categories?: Category[];
  featuredFlg?: boolean;
  hideInMarketPlace?: boolean;
  tenantId?: string;
  visitors?: IVisitor[];
  background?: string;
  headingBackground?: string;
  fanpassId?: string;
  discountForFanpassUser?: boolean;
  type?: string;
  authRequired?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
  isDeleted?: boolean;
}
