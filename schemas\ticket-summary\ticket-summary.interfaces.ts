export enum ITicketSummaryType {
    ZONE = 'zone',
    TICKET_CLASS = 'ticketClass',
}

export interface ITicketSummary {
    _id?: string;
    id?: string;
    eventId: string;
    type: ITicketSummaryType;
    referenceId: string;
    ticketClassId: string;
    totalTickets: number;
    holdTickets: number;
    soldTickets: number;
    blockedTickets: number;
    availableTickets: number;
    createdAt?: Date;
    updatedAt?: Date;
    isDeleted?: boolean;
}  