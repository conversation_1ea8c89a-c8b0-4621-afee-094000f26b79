import { LoggerService } from '@nestjs/common';
import { blue, cyan, gray, green, red, yellow } from 'colorette';

export class CustomLogger implements LoggerService {
  private readonly isProduction: boolean;

  constructor(private readonly context?: string) {
    this.isProduction = process.env.NODE_ENV === 'production';
  }


  private getTimestamp(): string {
    const localeStringOptions = {
      year: 'numeric',
      hour: 'numeric',
      minute: 'numeric',
      second: 'numeric',
      day: '2-digit',
      month: '2-digit',
    };
    return new Date().toLocaleString(undefined, localeStringOptions as Intl.DateTimeFormatOptions);
  }

  private formatMessage(level: string, message: string, context?: string): string {
    if (this.isProduction) {
      return JSON.stringify({
        timestamp: this.getTimestamp(),
        pid: process.pid,
        level,
        context: context || this.context,
        message
      });
    }

    const timestamp = gray(`[Nest] ${process.pid}  - ${this.getTimestamp()}`);
    const formattedLevel = this.colorize(level);
    const contextStr = yellow(`[${context || this.context}] `);
    return `${timestamp}   ${formattedLevel} ${contextStr}${message}`;
  }

  private colorize(level: string): string {
    switch (level) {
      case 'LOG':
        return green(`${level} `);
      case 'ERROR':
        return red(`${level}`);
      case 'WARN':
        return yellow(`${level} `);
      case 'DEBUG':
        return blue(`${level}`);
      case 'VERBOSE':
        return cyan(`${level}`);
      default:
        return level;
    }
  }

  log(message: string, context?: string) {
    console.log(this.formatMessage('LOG', message, context));
  }

  error(message: string, metadata?: any) {
    const baseLog = this.formatMessage('ERROR', message);
    if (metadata) {
      console.error(baseLog);
      console.error(red('Details:'), metadata);
    } else {
      console.error(baseLog);
    }
  }

  warn(message: string, context?: string) {
    console.warn(this.formatMessage('WARN', message, context));
  }

  debug(message: string, context?: string) {
    console.debug(this.formatMessage('DEBUG', message, context));
  }

  verbose(message: string, context?: string) {
    console.log(this.formatMessage('VERBOSE', message, context));
  }
}
