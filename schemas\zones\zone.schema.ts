import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { BaseDocument, BaseSchema, addSoftDeleteHooks, addUpdateHooks, defaultSchemaOptions } from '../index';

export type ZoneDocument = HydratedDocument<Zone> & BaseDocument;

@Schema({
    collection: 'zones',
    ...defaultSchemaOptions,
})
export class Zone {

    @Prop({
        default: 0,
    })
    position: number;

    @Prop({
        type: String,
    })
    name: string;

    @Prop({
        required: true,
        index: true,
    })
    calendarId: string;

    @Prop({
        required: true,
        index: true,
    })
    eventId: string;

    @Prop({
        required: true,
        index: true,
    })
    ticketClassId: string;

}

export const ZoneSchema = SchemaFactory.createForClass(Zone);
// Thêm các hooks và middleware
addSoftDeleteHooks(ZoneSchema);
addUpdateHooks(ZoneSchema);

