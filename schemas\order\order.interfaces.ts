
export enum IPaymentGatewayTypes {
  FREE = 'free',
  MOMO = 'momo',
  ZALOPAY = 'zalopay',
  ZALOPAY_VIETQR = 'zalopay_vietqr',
  ZALOPAY_CC = 'zalopay_cc',
  VNPAY = 'vnpay',
  INTCARD = 'intcard',
  PAYPAL = 'paypal',
  PAYMENTWALL = 'paymentwall',
  COD = 'cod',
  MANUALLY = 'manually',
}

export enum IOrderStatus {
  PENDING = 'PENDING',
  REVIEW = 'REVIEW',
  OPEN = 'OPEN',
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
  CANCELED = 'CANCELED',
  CANCELING = 'CANCELING',
  EXPIRED = 'EXPIRED',
  CONFIRM = 'CONFIRM',
  USERCANCEL = 'CANCELED',
  PAYMENT = 'PAYMENT',
  // đơn hàng huỷ thất bại
  CANCELEDFAILED = 'CANCELEDFAILED',
}


export enum IProductType {
  EVENT = 'event',
  FANPASS = 'fanpass',
  MERCHANDISE = 'merchandise',
}


export enum IDeliveryStatus {
  PENDING = 'pending',
  DELIVERED = 'delivered',
  CANCELED = 'canceled',
}


export enum ICurrencyCode {
  VND = 'VND',
  USD = 'USD',
}

export interface IOrderProduct {
  id: string;
  name: string;
  price: number;
  thumbnail: string;
  promotion: Record<string, any>;
  quantity: number;
  totalAmount: number;
}

export interface IPromotionUsed {
  code: string;
  discountPercent: number;
  maxValue: number;
  discountPercentInUSD: number;
}

export interface IOrder {
  _id?: string;
  id?: string;
  eventId: string;
  status: IOrderStatus;
  userId: string;
  productType: IProductType;
  paymentGateway?: IPaymentGatewayTypes;
  tickets: Record<string, any>[];
  paymentTime?: Date;
  deliveryStatus?: IDeliveryStatus;
  products: IOrderProduct[];
  promotionUsed?: Record<string, any>;
  currencyCode?: ICurrencyCode;
  originalAmount?: number;
  originalAmountUSD?: number;
  amount?: number;
  amountInUSD?: number;
  userRefId?: string;
  confirmAt?: Date;
  sellFromPlatform?: string;
  cancelReason?: string;
  quantity: number;
  calendarId: string;
  zoneId?: string;
  ticketClassId: string;
  timeCreatePayment?: Date;
  createdAt?: Date;
  updatedAt?: Date;
  isDeleted?: boolean;
  note?: string;
  transId?: string;
  numberOfTicketsDelivered: number;
  paymentInfo?: Record<string, any>
  extraData?: Record<string, any>;
}
