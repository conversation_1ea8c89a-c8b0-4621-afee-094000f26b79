import {
  ArgumentMetadata,
  BadRequestException,
  Injectable,
  ValidationPipe,
  ValidationPipeOptions
} from '@nestjs/common';

@Injectable()
export class CustomValidationPipe extends ValidationPipe {

  constructor(options?: ValidationPipeOptions) {
    super({
      whitelist: true, // Loại bỏ các trường không có trong DTO
      transform: true, // Tự động chuyển đổi dữ liệu đầu vào thành class instance của DTO
      forbidNonWhitelisted: true, // Báo lỗi nếu có trường không được định nghĩa trong DTO
      validationError: { target: false, value: false }, // Không hiển thị target và value trong lỗi
      ...options, // Cho phép truyền thêm tùy chọn nếu cần
    });
  }
  public async transform(value, metadata: ArgumentMetadata) {
    try {
      return await super.transform(value, metadata);
    } catch (e) {
      if (e instanceof BadRequestException) {
        const response = e.getResponse() as any;
        
        const errors: any = [];
        
        // Kiểm tra cấu trúc response
        const messages = Array.isArray(response['message']) 
          ? response['message'] 
          : [response['message']];
     
        for (const err of messages) {
          if (typeof err === 'string') {
            errors.push({
              field: 'unknown',
              message: err,
              constraints: { error: err }
            });
          } else {
            // Lấy tất cả các thông báo lỗi từ constraints
            const constraintMessages = err.constraints 
              ? Object.values(err.constraints) 
              : [`Validation failed for '${err.property}'`];
            
            errors.push({
              field: err.property,
              message: constraintMessages[0], // Lấy thông báo lỗi đầu tiên
              constraints: this.getConstraints(err),
              children: err.children?.length ? this.formatChildren(err.children) : undefined
            });
          }
        }
        
        throw new BadRequestException({
          statusCode: 400,
          message: 'Validation failed',
          errors,
        });
      }
      
      throw e;
    }
  }

  getConstraints(err) {
    if (typeof err == 'string') {
      return err;
    }
    if (typeof err == 'object' && err.constraints) {
      return err.constraints;
    }
    return this.deepReduce(err.children || []);
  }

  deepReduce(array) {
    if (!array || !array.length) return {};
    
    return array.reduce((accumulator, currentValue) => {
      if (currentValue.children && currentValue.children.length > 0) {
        return this.deepReduce(currentValue.children);
      }
      if (currentValue.constraints) {
        return currentValue.constraints;
      }
      return accumulator;
    }, {});
  }

  // Định dạng lỗi cho các trường con (nested objects)
  formatChildren(children) {
    if (!children || !children.length) return [];
    
    return children.map(child => {
      const result: any = {
        field: child.property,
        message: child.constraints 
          ? Object.values(child.constraints)[0] 
          : `Validation failed for '${child.property}'`,
      };
      
      if (child.constraints) {
        result.constraints = child.constraints;
      }
      
      if (child.children?.length) {
        result.children = this.formatChildren(child.children);
      }
      
      return result;
    });
  }
}
