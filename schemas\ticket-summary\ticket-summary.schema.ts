import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { BaseDocument, BaseSchema, addSoftDeleteHooks, addUpdateHooks, defaultSchemaOptions } from '../index';
import { ITicketSummaryType } from './ticket-summary.interfaces';

export type TicketSummaryDocument = HydratedDocument<TicketSummary> & BaseDocument;

@Schema({
    collection: 'ticket_summaries',
    ...defaultSchemaOptions,
})
export class TicketSummary extends BaseSchema {

    @Prop({ required: true, index: true })
    eventId: string;

    @Prop({ required: true, enum: Object.values(ITicketSummaryType), index: true })
    type: string;

    @Prop({ required: true, index: true })
    referenceId: string;

    @Prop({ required: false })
    ticketClassId: string;

    @Prop({ required: true, default: 0 })
    totalTickets: number;

    @Prop({ default: 0 })
    holdTickets: number;

    @Prop({ default: 0 })
    soldTickets: number;

    @Prop({ default: 0 })
    blockedTickets: number;

    @Prop({ required: true, default: 0 })
    availableTickets: number;

    @Prop({ type: Object, required: true, default: {} })
    data: Record<string, any>;
}

export const TicketSummarySchema = SchemaFactory.createForClass(TicketSummary);
addUpdateHooks(TicketSummarySchema);
addSoftDeleteHooks(TicketSummarySchema);

TicketSummarySchema.index({ eventId: 1, type: 1, referenceId: 1 });
TicketSummarySchema.index({ eventId: 1, referenceId: 1 }, { unique: true });
