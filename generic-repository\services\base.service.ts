import { Document, FilterQuery, UpdateQuery, QueryOptions, PipelineStage, UpdateWriteOpResult, ClientSession } from 'mongoose';
import { IBaseRepository } from '../repositories/base.repository.interface';
import { IBaseService } from './base.service.interface';

export class BaseService<T extends Document> implements IBaseService<T> {
  constructor(
    protected readonly repository: IBaseRepository<T>,
    context?: string,
  ) { }

  /**
  * Thực hiện một loạt các thao tác trong một transaction
  */
  async withTransaction<TResult>(
    callback: (session: ClientSession) => Promise<TResult>,
    options?: any
  ): Promise<TResult> {
    return this.repository.withTransaction(callback, options);
  }


  /**
   * S<PERSON> dụng explain để phân tích truy vấn find
   */
  async explain(
    filterQuery: FilterQuery<T>,
    projection?: Record<string, unknown>,
    options?: QueryOptions,
    verbosity: 'queryPlanner' | 'executionStats' | 'allPlansExecution' = 'executionStats',
  ): Promise<any> {
    return this.repository.explain(filterQuery, projection, options, verbosity);
  }

  /**
   * Tìm một document theo điều kiện
   */
  async findOne(
    filterQuery: FilterQuery<T>,
    projection?: Record<string, unknown>,
    options?: QueryOptions,
  ): Promise<T | null> {
    return this.repository.findOne(filterQuery, projection, options);
  }

  /**
   * Tìm một document theo điều kiện hoặc throw exception nếu không tìm thấy
   */
  async findOneOrFail(
    filterQuery: FilterQuery<T>,
    projection?: Record<string, unknown>,
    options?: QueryOptions,
  ): Promise<T> {
    return this.repository.findOneOrFail(filterQuery, projection, options);
  }

  /**
   * Tìm nhiều documents theo điều kiện
   */
  async find(
    filterQuery: FilterQuery<T>,
    projection?: Record<string, unknown>,
    options?: QueryOptions,
  ): Promise<T[]> {
    return this.repository.find(filterQuery, projection, options);
  }

  /**
   * Tạo một document mới
   */
  async create(createDto: Partial<T>): Promise<T> {
    return this.repository.create(createDto);
  }

  /**
   * Tạo nhiều documents
   */
  async insertMany(createDtos: Partial<T>[]): Promise<T[]> {
    return this.repository.insertMany(createDtos);
  }

  /**
   * Cập nhật một document theo điều kiện
   */
  async update(
    filterQuery: FilterQuery<T>,
    updateQuery: UpdateQuery<T>,
    options: QueryOptions = { new: true },
  ): Promise<T | null> {
    return this.repository.findOneAndUpdate(filterQuery, updateQuery, options);
  }

  updateOne(
    filterQuery: FilterQuery<T>,
    updateQuery: UpdateQuery<T>,
    options?: any,
  ): Promise<UpdateWriteOpResult> {
    return this.repository.updateOne(filterQuery, updateQuery, options);
  }

  /**
   * Cập nhật một document theo điều kiện hoặc throw exception nếu không tìm thấy
   */
  async updateOrFail(
    filterQuery: FilterQuery<T>,
    updateQuery: UpdateQuery<T>,
    options: QueryOptions = { new: true },
  ): Promise<T> {
    return this.repository.findOneAndUpdateOrFail(filterQuery, updateQuery, options);
  }

  /**
   * Cập nhật nhiều documents theo điều kiện
   */
  async updateMany(
    filterQuery: FilterQuery<T>,
    updateQuery: UpdateQuery<T>,
  ): Promise<{ matchedCount: number; modifiedCount: number }> {
    return this.repository.updateMany(filterQuery, updateQuery);
  }

  /**
   * Xóa một document theo điều kiện
   */
  async delete(
    filterQuery: FilterQuery<T>,
    options?: QueryOptions,
  ): Promise<T | null> {
    return this.repository.findOneAndDelete(filterQuery, options);
  }

  /**
   * Xóa một document theo điều kiện hoặc throw exception nếu không tìm thấy
   */
  async deleteOrFail(
    filterQuery: FilterQuery<T>,
    options?: QueryOptions,
  ): Promise<T> {
    return this.repository.findOneAndDeleteOrFail(filterQuery, options);
  }

  /**
   * Xóa nhiều documents theo điều kiện
   */
  async deleteMany(filterQuery: FilterQuery<T>): Promise<{ deletedCount: number }> {
    return this.repository.deleteMany(filterQuery);
  }

  /**
   * Đếm số lượng documents theo điều kiện
   */
  async count(filterQuery: FilterQuery<T>): Promise<number> {
    return this.repository.count(filterQuery);
  }

  /**
   * Kiểm tra document có tồn tại không
   */
  async exists(filterQuery: FilterQuery<T>): Promise<boolean> {
    return this.repository.exists(filterQuery);
  }

  /**
   * Thực hiện aggregation pipeline
   */
  async aggregate(pipeline: PipelineStage[]): Promise<any[]> {
    return this.repository.aggregate(pipeline);
  }

  /**
   * Phân trang
   */
  async pagination(
    filterQuery: FilterQuery<T>,
    options: {
      page?: number;
      limit?: number;
      sort?: Record<string, 1 | -1>;
      projection?: Record<string, unknown>;
    } = {},
  ): Promise<{
    data: T[];
    pagination: {
      total: number;
      page: number;
      limit: number;
      pages: number;
    };
  }> {
    return this.repository.pagination(filterQuery, options);
  }

  /**
   * Tìm và cập nhật một document với các tùy chọn mở rộng
   */
  async findOneAndUpdate(
    filterQuery: FilterQuery<T>,
    updateQuery: UpdateQuery<T>,
    options: QueryOptions = { new: true }
  ): Promise<T | null> {
    return this.repository.findOneAndUpdate(filterQuery, updateQuery, options);
  }

  /**
   * Tìm và cập nhật một document với các tùy chọn mở rộng hoặc throw exception nếu không tìm thấy
   */
  async findOneAndUpdateOrFail(
    filterQuery: FilterQuery<T>,
    updateQuery: UpdateQuery<T>,
    options: QueryOptions = { new: true }
  ): Promise<T> {
    return this.repository.findOneAndUpdateOrFail(filterQuery, updateQuery, options);
  }

}
