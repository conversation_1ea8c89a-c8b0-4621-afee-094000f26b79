import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { BaseDocument, BaseSchema, addSoftDeleteHooks, addUpdateHooks, defaultSchemaOptions } from '../index';

export type TicketClassDocument = HydratedDocument<TicketClass> & BaseDocument;

@Schema({
    collection: 'ticket_classes',
    ...defaultSchemaOptions,
})
export class TicketClass extends BaseSchema {
    @Prop({ required: true, index: true })
    eventId: string;

    @Prop({ required: true, index: true })
    calendarId: string;

    @Prop({})
    name: string;

    @Prop({})
    color: string;

    @Prop({})
    originalPriceVn: number;

    @Prop({})
    originalPriceUsd: number;

    @Prop({})
    finalPriceVn: number;

    @Prop({})
    finalPriceUsd: number;

    @Prop({})
    description: string;

    @Prop({})
    seatType: string;

    @Prop({})
    maxTicketPerUser: number;

    @Prop({})
    prototypeUrl: string;
}

export const TicketClassSchema = SchemaFactory.createForClass(TicketClass);
addUpdateHooks(TicketClassSchema);
addSoftDeleteHooks(TicketClassSchema);
