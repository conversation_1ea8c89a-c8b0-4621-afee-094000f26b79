import { Injectable, Inject, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { Cluster } from 'ioredis';
import { ConfigService } from '@nestjs/config';
import { CustomLogger } from '../../logger/custom.logger';

@Injectable()
export class RedisService implements OnModuleInit, OnModuleDestroy {
  private readonly customLogger = new CustomLogger('RedisService');
  private connectionHealthCheckInterval: NodeJS.Timeout;
  private reconnectAttempts = 0;
  private readonly maxReconnectAttempts = 20;

  constructor(
    @Inject('REDIS_CLUSTER') private readonly redis: Cluster,
    private readonly configService: ConfigService,
  ) {}

  async onModuleInit() {
    this.customLogger.log('Initializing Redis Service...');
    
    // Kiểm tra kết nối Redis mỗi 30 giây
    this.connectionHealthCheckInterval = setInterval(() => {
      this.checkConnectionHealth();
    }, 30000);

    // Thêm xử lý lỗi cho Redis
    this.redis.on('error', (err) => {
      this.customLogger.error(`Redis error: ${err.message}`, err.stack);
      this.attemptReconnect();
    });

    this.redis.on('close', () => {
      this.customLogger.warn('Redis connection closed');
      this.attemptReconnect();
    });

    // Kiểm tra kết nối ban đầu
    try {
      await this.ping();
      this.customLogger.log('Redis Cluster connection successful');
    } catch (error) {
      this.customLogger.error('Failed to connect to Redis Cluster', error.stack);
      this.attemptReconnect();
    }
  }

  /**
   * Thử kết nối lại với Redis Cluster
   */
  private attemptReconnect() {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      this.customLogger.error(`Failed to reconnect to Redis after ${this.maxReconnectAttempts} attempts`);
      return;
    }

    this.reconnectAttempts++;
    const delay = Math.min(this.reconnectAttempts * 1000, 10000); // Tối đa 10 giây

    this.customLogger.log(`Attempting to reconnect to Redis (attempt ${this.reconnectAttempts}) in ${delay}ms...`);
    
    setTimeout(async () => {
      try {
        // Thử đóng kết nối hiện tại nếu còn tồn tại
        try {
          this.redis.disconnect(false);
        } catch (err) {
          // Bỏ qua lỗi khi đóng kết nối
        }

        // Thử kết nối lại
        await this.redis.connect();
        
        // Kiểm tra kết nối
        const isConnected = await this.ping();
        if (isConnected) {
          this.customLogger.log('Successfully reconnected to Redis Cluster');
          this.reconnectAttempts = 0;
        } else {
          throw new Error('Ping failed after reconnect');
        }
      } catch (error) {
        this.customLogger.error(`Reconnect attempt ${this.reconnectAttempts} failed`, error.stack);
        this.attemptReconnect(); // Thử lại
      }
    }, delay);
  }

  /**
   * Kiểm tra sức khỏe kết nối Redis
   */
  private async checkConnectionHealth() {
    try {
      const isConnected = await this.ping();
      
      if (isConnected) {
        // // Kiểm tra thông tin cluster
        // const info = await this.getClusterInfo();
        // if (info) {
        //   this.customLogger.log(`Redis Cluster health check: OK`);
        //   this.customLogger.log(`Cluster state: ${info.cluster_state}`);
        //   this.customLogger.log(`Cluster slots assigned: ${info.cluster_slots_assigned}`);
        //   this.customLogger.log(`Cluster slots ok: ${info.cluster_slots_ok}`);
        // }
      } else {
        this.customLogger.warn('Redis Cluster ping failed, connection may not be healthy');
        this.attemptReconnect();
      }
    } catch (error) {
      this.customLogger.error('Failed to check Redis Cluster connection', error.stack);
      this.attemptReconnect();
    }
  }

  /**
   * Lấy thông tin về cluster
   */
  private async getClusterInfo(): Promise<Record<string, string> | null> {
    try {
      const result = await this.redis.cluster('INFO');
      const info: Record<string, string> = {};
      
      if (typeof result === 'string') {
        const lines = result.split('\r\n');
        for (const line of lines) {
          if (line && !line.startsWith('#')) {
            const [key, value] = line.split(':');
            if (key && value) {
              info[key.trim()] = value.trim();
            }
          }
        }
        return info;
      }
      
      return null;
    } catch (error) {
      this.customLogger.error('Failed to get cluster info', error.stack);
      return null;
    }
  }

  async onModuleDestroy() {
    this.customLogger.log('Closing Redis Cluster connection...');
    
    // Xóa interval check
    if (this.connectionHealthCheckInterval) {
      clearInterval(this.connectionHealthCheckInterval);
    }
    
    try {
      // Wait for all pending commands to complete before closing the connection
      await this.redis.quit();
      this.customLogger.log('Redis Cluster connection closed gracefully');
    } catch (error) {
      this.customLogger.error('Error closing Redis Cluster connection', error.stack);
      // If quit() fails, try disconnect() as a fallback
      try {
        // Disconnect immediately without waiting for pending commands
        this.redis.disconnect();
        this.customLogger.log('Redis Cluster connection force closed after quit failed');
      } catch (disconnectError) {
        this.customLogger.error('Failed to force close Redis Cluster connection', disconnectError.stack);
      }
    }
  }

  getClient(): Cluster {
    return this.redis;
  }

  async ping(): Promise<boolean> {
    try {
      const result = await this.redis.ping();
      return result === 'PONG';
    } catch (error) {
      this.customLogger.error('Redis ping failed', error.stack);
      return false;
    }
  }

  /**
   * Wrapper cho các lệnh Redis với retry logic
   */
  async executeWithRetry<T>(
    command: string,
    args: any[] = [],
    maxRetries = 3
  ): Promise<T> {
    let retries = 0;
    let lastError: Error;

    while (retries <= maxRetries) {
      try {
        // @ts-ignore
        return await this.redis[command](...args);
      } catch (error) {
        lastError = error;
        
        // Nếu lỗi kết nối, thử kết nối lại
        if (
          error.message.includes('Connection is closed') ||
          error.message.includes('Connection timeout') ||
          error.message.includes('failed to refresh slots cache')
        ) {
          this.customLogger.warn(`Redis command ${command} failed (attempt ${retries + 1}/${maxRetries + 1}): ${error.message}`);
          
          // Tăng thời gian chờ theo số lần thử
          const delay = Math.min((retries + 1) * 200, 2000);
          await new Promise(resolve => setTimeout(resolve, delay));
          
          retries++;
          continue;
        }
        
        // Các lỗi khác, ném ra ngay
        throw error;
      }
    }
    
    // Nếu đã thử hết số lần mà vẫn lỗi
    throw lastError;
  }

  async getInfo(): Promise<any> {
    try {
      const infoStr = await this.redis.info();
      const info = {};

      infoStr.split('\r\n').forEach(line => {
        if (line && !line.startsWith('#')) {
          const parts = line.split(':');
          if (parts.length === 2) {
            info[parts[0]] = parts[1];
          }
        }
      });

      return info;
    } catch (error) {
      this.customLogger.error('Failed to get Redis info', error.stack);
      return {};
    }
  }

  async get(key: string): Promise<string | null> {
    try {
      return await this.redis.get(key);
    } catch (error) {
      this.customLogger.error(`Error getting key ${key} from Redis`, error.stack);
      throw error;
    }
  }

  async set(key: string, value: string, ttl?: number): Promise<'OK'> {
    try {
      if (ttl) {
        return await this.redis.set(key, value, 'EX', ttl);
      }
      return await this.redis.set(key, value);
    } catch (error) {
      this.customLogger.error(`Error setting key ${key} in Redis`, error.stack);
      throw error;
    }
  }

  async del(key: string): Promise<number> {
    try {
      return await this.redis.del(key);
    } catch (error) {
      this.customLogger.error(`Error deleting key ${key} from Redis`, error.stack);
      throw error;
    }
  }

  async incr(key: string): Promise<number> {
    return this.redis.incr(key);
  }

  async decr(key: string): Promise<number> {
    return this.redis.decr(key);
  }

  async incrby(key: string, increment: number): Promise<number> {
    return this.redis.incrby(key, increment);
  }

  async decrby(key: string, decrement: number): Promise<number> {
    return this.redis.decrby(key, decrement);
  }

  // Thêm phương thức để thực hiện nhiều lệnh Redis trong một pipeline
  async pipeline(commands: Array<[string, ...any[]]>): Promise<any[]> {
    const pipeline = this.redis.pipeline();

    for (const [command, ...args] of commands) {
      pipeline[command](...args);
    }

    return pipeline.exec();
  }

  // Tối ưu phương thức executeScript
  async executeScript(script: string, keys: string[], args: string[]): Promise<any> {
    // Sử dụng defineCommand để tối ưu hiệu suất cho các script được sử dụng nhiều lần
    const scriptHash = require('crypto').createHash('sha1').update(script).digest('hex');

    // Kiểm tra xem script đã được cache chưa
    try {
      return await this.redis.evalsha(scriptHash, keys.length, ...keys, ...args);
    } catch (err) {
      if (err.message.includes('NOSCRIPT')) {
        // Script chưa được cache, thực thi và cache
        return this.redis.eval(script, keys.length, ...keys, ...args);
      }
      throw err;
    }
  }


  async releaseLock(lockKey: string, value: string): Promise<boolean> {
    const script = `
      if redis.call("get", KEYS[1]) == ARGV[1] then
        return redis.call("del", KEYS[1])
      else
        return 0
      end
    `;
    const result = await this.redis.eval(script, 1, lockKey, value);
    return result === 1;
  }

  // Thêm phương thức zadd để hỗ trợ sorted sets
  async zadd(key: string, ...args: string[]): Promise<number> {
    return this.redis.zadd(key, ...args);
  }

  // Thêm các phương thức khác cho sorted sets
  async zrevrange(key: string, start: number, stop: number, withScores?: boolean): Promise<string[]> {
    if (withScores) {
      return this.redis.zrevrange(key, start, stop, 'WITHSCORES');
    }
    return this.redis.zrevrange(key, start, stop);
  }

  async zrange(key: string, start: number, stop: number, withScores?: boolean): Promise<string[]> {
    if (withScores) {
      return this.redis.zrange(key, start, stop, 'WITHSCORES');
    }
    return this.redis.zrange(key, start, stop);
  }

  async zrem(key: string, ...members: string[]): Promise<number> {
    return this.redis.zrem(key, ...members);
  }

  async zscore(key: string, member: string): Promise<string | null> {
    return this.redis.zscore(key, member);
  }


  async hgetall(key: string): Promise<Record<string, string>> {
    return this.redis.hgetall(key);
  }
}
