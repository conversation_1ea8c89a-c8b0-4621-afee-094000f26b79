import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import * as jwt from 'jsonwebtoken';
import { validate } from 'uuid';
import { ConfigService } from '@nestjs/config';
import { IS_PUBLIC_KEY } from '../decorators/public.decorator';

@Injectable()
export class AuthGuard implements CanActivate {
  constructor(
    private readonly reflector: Reflector,
    private readonly configService: ConfigService,
  ) { }

  canActivate(context: ExecutionContext): boolean | Promise<boolean> {
    if (context.getType() !== 'http') {
      return true;
    }
    const isPublic = this.reflector.get<boolean>(IS_PUBLIC_KEY, context.getHandler());
    if (isPublic) {
      return true;
    }
    const request = context.switchToHttp().getRequest(); // Chuyển ExecutionContext sang HTTP context

    const { headers } = request;

    // Extract token from header, URL params, or POST body
    const token =
      headers?.authorization || request?.query?.token || request?.body?.token;

    if (!token) {
      throw new UnauthorizedException('Token not provided');
    }

    // Check if token is a UUID (AnonymousId)
    if (validate(token)) {
      //Load testing
      if (this.configService.get('LOAD_TEST')) {
        request.user = {
          email: token,
        };
        return true;
      } else {
        throw new UnauthorizedException('Token not provided');
      }
    }

    // Match Bearer token
    const match = token.match(/^Bearer (.*)$/);
    if (!match || match.length < 2) {
      throw new UnauthorizedException('Invalid Bearer token');
    }

    const extractedToken = match[1];

    try {
      // Verify JWT token
      const decoded = jwt.verify(
        extractedToken,
        this.configService.get<string>('CLIENT_SECRET_KEY'),
      );
      request.user = decoded;
      return true;
    } catch (err) {
      throw new UnauthorizedException('Invalid or expired token');
    }
  }
}
