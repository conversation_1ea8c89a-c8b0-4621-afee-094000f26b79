
export enum IPromotionAppliesTo {
    EVENT = 'event',
    TICKET = 'ticket'
}

export interface IPromotionDiscount {
    percent: number;
    percentInUSD: number;
    maxValue: number;
    maxValueInUSD: number;
}

export enum IPromotionType {
    AUTO = 'auto',
    MANUALLY = 'manually'
}

export interface IPromotion {
    _id?: string;
    id?: string;
    eventId: string;
    code: string;
    appliesTo: IPromotionAppliesTo;
    active: boolean;
    maxUsage: number;
    discount: IPromotionDiscount;
    tickets: Record<string, any>[];
    type: IPromotionType;
    minOrderAmount: number;
    minOrderAmountInUSD: number;
    createdAt?: Date;
    updatedAt?: Date;
    isDeleted?: boolean;
}