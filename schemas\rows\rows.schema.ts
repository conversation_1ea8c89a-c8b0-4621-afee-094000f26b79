import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { BaseDocument, BaseSchema, addSoftDeleteHooks, addUpdateHooks, defaultSchemaOptions } from '../index';

export type RowDocument = HydratedDocument<Row> & BaseDocument;

@Schema({
    collection: 'rows',
    ...defaultSchemaOptions,
})
export class Row extends BaseSchema {
    @Prop({})
    calendarId: string;

    @Prop({
        required: true,
        index: true,
    })
    eventId: string;

    @Prop({
        required: true,
        index: true,
    })
    zoneId: string;

    @Prop({})
    seatNumberingType: string;

    @Prop({})
    seatNumberingFrom: number;

    @Prop({})
    seatNumberingTo: number;

    @Prop({})
    name: string;

    @Prop({})
    position: number
}

export const RowSchema = SchemaFactory.createForClass(Row);

// Thêm các hooks và middleware
addSoftDeleteHooks(RowSchema);
addUpdateHooks(RowSchema);
