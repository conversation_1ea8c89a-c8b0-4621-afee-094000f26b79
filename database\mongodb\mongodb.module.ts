import { Module, Global } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongodbService } from './mongodb.service';

@Global() // Đảm bảo module này có thể được sử dụng trong các module khác(không cần import lại)
@Module({
  imports: [
    MongooseModule.forRootAsync({
      imports: [ConfigModule],// ConfigModule: là nơi chứa các biến môi trường
      inject: [ConfigService], // tiêm ConfigService VÀO HÀ useFactory
      useFactory: async (configService: ConfigService) => {
        const uri = configService.get<string>('MONGODB_URI');
        return {
          uri, //đường kết nối MongoDB, lấy từ biến môi trường MONGODB_URI
          // Tăng kích thước pool để xử lý nhiều kết nối hơn
          maxPoolSize: configService.get<number>('MONGODB_MAX_POOL_SIZE', 200), // Tăng từ 100 lên 200
          minPoolSize: configService.get<number>('MONGODB_MIN_POOL_SIZE', 10), // Tăng từ 5 lên 10
          maxConnecting: configService.get<number>('MONGODB_MAX_CONNECTING', 10), // Tăng từ 5 lên 10
          
          // Tăng thời gian chờ hàng đợi
          waitQueueTimeoutMS: configService.get<number>('MONGODB_WAIT_QUEUE_TIMEOUT_MS', 10000), // Tăng từ 5000 lên 10000
          
          
          maxIdleTimeMS: configService.get<number>('MONGODB_MAX_IDLE_TIME_MS', 60000),
          connectTimeoutMS: 30000,
          socketTimeoutMS: 60000,
          heartbeatFrequencyMS: 10000,
          serverSelectionTimeoutMS: 30000,
          retryWrites: true,
          retryReads: true,
          compressors: 'zlib',
          zlibCompressionLevel: 6,
          monitorCommands: true,
        };
      },
      // → Nghĩa là: "Tôi muốn khởi tạo MongoDB, và lấy MONGODB_URI từ file .env thông qua ConfigService
    }),
  ],
  providers: [MongodbService],
  exports: [MongodbService, MongooseModule],
})
export class MongodbModule {}
