export enum ITicketStatus {
    IDLE = 'idle',
    BLOCK = 'block',
    SOLD = 'sold',
    HOLD = 'hold',
    CANCEL = 'cancel',
    CHECKIN= 'checkin',
};


export interface ITickets {
    _id?: string;
    calendarId: string;
    eventId: string;
    rowId: string;
    zoneId: string;
    code?: string;
    status: string;
    checkin?: boolean;
    customerName?: string;
    customerEmail?: string;
    customerPhone?: string;
    customerNote?: string;
    customerExtraData?: string;
    orderId?: string;
    ticketClassId: string;
    position: number;
    generateCode?: string;
    holdAt?: Date;
    soldAt?: Date;
    paymentGateway?: string;
    checkedInBy?: string;
    checkinTime?: Date;
    createdAt?: Date;
    updatedAt?: Date;
    isDeleted?: boolean;
}  