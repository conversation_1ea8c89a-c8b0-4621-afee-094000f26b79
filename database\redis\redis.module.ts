import { Module, Global, Provider } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { RedisService } from './redis.service';
import { CustomLogger } from '../../logger/custom.logger';
import { Cluster } from 'ioredis';

// Provider cho Redis Cluster
const RedisClusterProvider: Provider = {
  provide: 'REDIS_CLUSTER',
  useFactory: (configService: ConfigService) => {
    const logger = new CustomLogger('RedisClusterModule');
    const Redis = require('ioredis');

    logger.log('Initializing Redis Cluster connection...');

    const host = configService.get<string>('REDIS_CLUSTER_HOST', 'localhost');
    const port = configService.get<number>('REDIS_CLUSTER_PORT', 6379);

    logger.log(`Connecting to AWS ElastiCache Redis Cluster at ${host}:${port}`);

    // Cấu hình tối ưu cho AWS ElastiCache
    const cluster = new Redis.Cluster(
      [
        {
          host: host,
          port: port,
        }
      ],
      {
        // Xử lý DNS đặc biệt cho AWS ElastiCache
        dnsLookup: (address: string, callback: (err: null, address: string) => void) => {
          callback(null, address);
        },

        // Chiến lược kết nối lại cho cluster - Tăng số lần thử và thời gian chờ
        clusterRetryStrategy: (times: number): number => {
          // Tăng thời gian chờ tối đa lên 5 giây
          const delay = Math.min(times * 200, 5000);
          logger.warn(
            `Retrying Redis Cluster connection: Attempt ${times}, Delay ${delay}ms`,
          );
          // Không giới hạn số lần thử lại
          return delay;
        },

        // Tùy chọn cho từng node Redis
        redisOptions: {
          // Cấu hình TLS cho AWS ElastiCache
          tls: {
            rejectUnauthorized: false,
            // Thêm các tùy chọn TLS để tăng độ ổn định
            checkServerIdentity: () => undefined, // Bỏ qua kiểm tra certificate
            secureProtocol: 'TLSv1_2_method', // Sử dụng TLS 1.2
          },

          // Tăng timeout cho kết nối và lệnh
          connectTimeout: Number(configService.get('REDIS_CONNECT_TIMEOUT', 10000)), // Tăng từ 5000 lên 10000
          commandTimeout: Number(configService.get('REDIS_COMMAND_TIMEOUT', 30000)), // Tăng từ 20000 lên 30000

          // Chiến lược kết nối lại cho từng node - Tăng số lần thử
          retryStrategy: (times) => {
            logger.warn(`Redis node connection attempt ${times} failed. Retrying...`);
            if (times > 10) { // Tăng từ 5 lên 10
              logger.error('Redis node connection failed after 10 attempts. Giving up.');
              return null; // stop retrying
            }
            const delay = Math.min(times * 100, 3000); // Tăng delay tối đa
            logger.log(`Retrying Redis node connection in ${delay}ms`);
            return delay;
          },

          // Thêm keepAlive để duy trì kết nối
          keepAlive: 10000, // Gửi keepalive packet mỗi 10 giây
          noDelay: true, // Tắt Nagle's algorithm
        },

        // Tối ưu hóa khám phá và kết nối
        enableReadyCheck: true,
        enableOfflineQueue: true,
        scaleReads: 'slave',

        // Tối ưu hóa khám phá slot - Tăng thời gian chờ
        slotsRefreshTimeout: 15000, // Tăng từ 10000 lên 15000
        slotsRefreshInterval: 15000, // Giảm từ 30000 xuống 15000 để cập nhật thường xuyên hơn

        // Tối ưu hóa chuyển hướng và thử lại - Tăng các giá trị
        maxRedirections: 20, // Tăng từ 16 lên 20
        retryDelayOnFailover: 200, // Tăng từ 100 lên 200
        retryDelayOnClusterDown: 2000, // Tăng từ 1000 lên 2000
        retryDelayOnTryAgain: 200, // Tăng từ 100 lên 200

        // Thêm các tùy chọn mới
        disconnectTimeout: 30000, // Thời gian chờ trước khi đóng kết nối
        autoResendUnfulfilledCommands: true, // Tự động gửi lại lệnh chưa hoàn thành
        maxRetriesPerRequest: 5, // Số lần thử lại tối đa cho mỗi request
      },
    );

    // Thêm event listeners để log các sự kiện kết nối
    cluster.on('connect', () => {
      logger.log('Connected to Redis Cluster');
    });

    // Xử lý sự kiện ready
    cluster.on('ready', () => {
      logger.log('Redis Cluster is ready');
      // Các xử lý hiện tại...
    });
    
    // Cải thiện xử lý lỗi
    cluster.on('error', (err) => {
      logger.error(`Redis Cluster error: ${err.message}`, err.stack);
      // Không đóng ứng dụng khi có lỗi Redis
    });

    // Xử lý sự kiện close
    cluster.on('close', () => {
      logger.warn('Redis Cluster connection closed - Will attempt to reconnect automatically');
    });

    // Xử lý sự kiện reconnecting
    cluster.on('reconnecting', () => {
      logger.log('Redis Cluster reconnecting');
    });

    // Xử lý sự kiện end
    cluster.on('end', () => {
      logger.warn('Redis Cluster connection ended - Will need manual reconnection');
      // Thử kết nối lại sau 5 giây
      setTimeout(() => {
        logger.log('Attempting to manually reconnect to Redis Cluster...');
        try {
          cluster.connect();
        } catch (error) {
          logger.error('Failed to manually reconnect to Redis Cluster', error.stack);
        }
      }, 5000);
    });

    // Xử lý lỗi node
    cluster.on('node error', (err, address) => {
      logger.error(`Redis Cluster node ${address} error: ${err.message}`, err.stack);
      // Không cần xử lý thêm vì ioredis sẽ tự động thử kết nối lại
    });

    cluster.on('+node', (node) => {
      logger.log(`New node added to Redis Cluster: ${node.options.host}:${node.options.port}`);
    });

    cluster.on('-node', (node) => {
      logger.log(`Node removed from Redis Cluster: ${node.options.host}:${node.options.port}`);
    });

    return cluster;
  },
  inject: [ConfigService],
};

@Global()
@Module({
  imports: [ConfigModule],
  providers: [
    RedisClusterProvider,
    RedisService,
  ],
  exports: [RedisService],
})
export class RedisModule { }
