import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { BaseDocument, BaseSchema, addSoftDeleteHooks, addUpdateHooks, defaultSchemaOptions } from '../index';
import { IOrderStatus, IPaymentGatewayTypes, IOrderProduct, IProductType, IDeliveryStatus, ICurrencyCode } from './order.interfaces';

export type OrderDocument = HydratedDocument<Order> & BaseDocument;

@Schema({
    collection: 'orders',
    ...defaultSchemaOptions,
})
export class Order extends BaseSchema {
    @Prop({
        required: true,
        index: true,
    })
    eventId: string;

    @Prop({
        required: true,
        enum: Object.values(IOrderStatus),
        default: IOrderStatus.PENDING,
        index: true,
    })
    status: IOrderStatus;

    @Prop({ required: true, index: true })
    userId: string;

    @Prop({
        required: true,
        enum: Object.values(IProductType),
        index: true,
    })
    productType: IProductType;

    @Prop({
        enum: Object.values(IPaymentGatewayTypes),
        index: true,
    })
    paymentGateway: IPaymentGatewayTypes;

    @Prop({ type: [Object], required: true, default: [] })
    tickets: Record<string, any>[];

    @Prop()
    paymentTime: Date;

    @Prop({
        enum: Object.values(IDeliveryStatus),
        default: IDeliveryStatus.PENDING,
    })
    deliveryStatus: IDeliveryStatus;

    @Prop({
        type: [
            {
                id: String,
                name: String,
                price: Number,
                thumbnail: String,
                promotion: Object,
                quantity: Number,
                totalAmount: Number,
            },
        ],
        default: [],
        _id: false,
    })
    products: IOrderProduct[];

    @Prop({ type: Object, default: {} })
    promotionUsed: Record<string, any>;


    @Prop({
        enum: Object.values(ICurrencyCode),

    })
    currencyCode: ICurrencyCode;

    @Prop() originalAmount: number;
    @Prop() originalAmountUSD: number;
    @Prop() amount: number;
    @Prop() amountInUSD: number;

    @Prop() userRefId: string;

    @Prop() confirmAt: Date;

    @Prop({ default: 'web' })
    sellFromPlatform: string;

    @Prop({ default: '' })
    cancelReason: string;

    @Prop({ required: true })
    quantity: number;

    @Prop({ required: true })
    calendarId: string;

    @Prop() zoneId: string;

    @Prop({ required: true, index: true })
    ticketClassId: string;

    @Prop()
    timeCreatePayment: Date;

    @Prop()
    orderId: string;

    // chưa định nghĩa extraData trong bảng order
    @Prop({ type: Object, default: {} })
    extraData: Record<string, any>;

    @Prop({ type: String, default: '' })
    note: string;

    @Prop({ type: String, default: '' })
    transId: string;

    @Prop({ type: Number, default: 0 })
    numberOfTicketsDelivered: number;

    @Prop({ type: Object, default: {} })
    paymentInfo: Record<string, any>;

    @Prop({ type: String, default: '' })
    message: string;

}

export const OrderSchema = SchemaFactory.createForClass(Order);

// Thêm các hooks và middleware
addSoftDeleteHooks(OrderSchema);
addUpdateHooks(OrderSchema);

// Thêm các index nếu cần
OrderSchema.index({ isDeleted: 1 });
OrderSchema.index({ userId: 1, status: 1 });
OrderSchema.index({ eventId: 1, status: 1 });
OrderSchema.index({ userId: 1, eventId: 1, status: 1, isDeleted: 1 });
OrderSchema.index({ status: 1 });

