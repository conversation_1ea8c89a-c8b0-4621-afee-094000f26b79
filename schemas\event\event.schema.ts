import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { BaseDocument, BaseSchema, addSoftDeleteHooks, addUpdateHooks, defaultSchemaOptions } from '../index';
import { IEventCalendar, IStatusEvent } from './event.interfaces';

export type EventDocument = HydratedDocument<Event> & BaseDocument;

@Schema({ _id: false })
export class SchemaPaymentInfoField {
    @Prop({ required: true })
    key: string;

    @Prop({ required: true })
    text: string;

    @Prop({ default: 'text' })
    type: string;

    @Prop({ default: false })
    isDefault: boolean;

    @Prop({ default: false })
    isRequired: boolean;
}

@Schema({ _id: false })
export class SchemaGeneral {
    @Prop({ type: String, default: 'eventista' })
    collaboration: string;

    @Prop({ type: String, default: 'hard_ticket' })
    ticketType: string;

    @Prop({ type: [SchemaPaymentInfoField], default: [] })
    paymentInfoFields: SchemaPaymentInfoField[];
}

@Schema({ _id: false }) // Nếu bạn không cần _id riêng cho mỗi visitor
export class SchemaVisitor {
    @Prop() name: string;
    @Prop() picture: string;
    @Prop() id: string;
}

@Schema({
    collection: 'events',
    ...defaultSchemaOptions,
})
export class Event extends BaseSchema {

    @Prop({
        required: true,
        unique: true,
        index: true
    })
    eventId: string;

    @Prop({ required: true })
    eventName: string;

    @Prop({ required: true })
    location: String;

    @Prop({ required: true })
    address: string;

    @Prop({ required: true })
    pathName: string;

    @Prop({ required: false })
    thumbnail: string;

    @Prop({ required: false })
    logo: string;

    @Prop({ required: false })
    venueMap: string;

    @Prop({ enum: Object.values(IStatusEvent), default: "OPEN" })
    status: string;

    @Prop({
        type: [
            {
                calendarId: { type: String, required: true },
                startDate: { type: Date, required: true },
                endDate: { type: Date, required: true },
                maxTicketPerUser: { type: Number, default: -1 },
            },
        ],
        required: true,
        default: [],
        _id: false,
    })
    eventCalendar: IEventCalendar[];

    @Prop({
        type: {
            thumbnail: { type: String, required: false },
            description: { type: String, required: false },
        },
        required: false,
        default: {},
        _id: false,
    })
    ticketCalendar: {
        thumbnail: String;
        description: String;
    };

    @Prop({})
    hostedBy: String

    // check lại  nếu bị lỗi khi tạo event
    @Prop({ type: [Object], required: true, default: [], _id: false }) // Mảng các object không xác định cấu trúc
    tickets: Record<string, any>[];

    @Prop({
        type: [
            {
                title: { type: String },
                description: { type: String },
            },
        ],
        required: false,
        default: [],
        _id: false,
    })
    eventDescriptions: { title: string; description: string }[];

    @Prop({
        type: Number,
        required: true,
        default: 0,
    })
    maxTicketPerUser: number;

    @Prop({ type: String, required: false })
    note: string;

    @Prop({ type: SchemaGeneral, default: {} })
    general: SchemaGeneral;

    @Prop({ type: String, required: true })
    domain: string;

    @Prop({
        type: [String],
        enum: ['concert', 'live_show', 'workshop', 'fan_meeting'],
        default: [],
        _id: false,
    })
    categories: ('concert' | 'live_show' | 'workshop' | 'fan_meeting')[];

    @Prop({ type: Boolean, default: false })
    featuredFlg: boolean;

    @Prop({ type: Boolean, default: false })
    hideInMarketPlace: boolean;

    @Prop({ type: String, required: false })
    tenantId: string;

    @Prop({ type: [SchemaVisitor], default: [] })
    visitors: SchemaVisitor[];

    @Prop({ type: String, required: false })
    background: string;

    @Prop({ type: String, required: false })
    headingBackground: string;

    @Prop({ type: String, required: false })
    fanpassId: string;

    @Prop({ type: Boolean, default: false })
    discountForFanpassUser: boolean;

    @Prop({ type: String, required: false })
    type: string;

    @Prop({ type: Boolean, default: false })
    authRequired: boolean;
}

export const EventSchema = SchemaFactory.createForClass(Event);

// Thêm các hooks và middleware
addSoftDeleteHooks(EventSchema);
addUpdateHooks(EventSchema);

//Thêm các index
EventSchema.index({ isDeleted: 1 });
EventSchema.index({ eventName: 1 });
EventSchema.index({ pathName: 1 });
EventSchema.index({ status: 1 });
EventSchema.index({ tenantId: 1 });

