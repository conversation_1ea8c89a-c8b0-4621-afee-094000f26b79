import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  HttpStatus,
} from '@nestjs/common';
import { CustomLogger } from '../logger/custom.logger';

@Catch()
export class GlobalExceptionFilter implements ExceptionFilter {
  private readonly logger = new CustomLogger(GlobalExceptionFilter.name);

  catch(exception: any, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse();
    const request = ctx.getRequest();

    // Lấy mã trạng thái
    const status =
      exception instanceof HttpException
        ? exception.getStatus()
        : HttpStatus.INTERNAL_SERVER_ERROR;

    // Xử lý lỗi validation từ CustomValidationPipe
    if (status === HttpStatus.BAD_REQUEST && exception instanceof HttpException) {
      const exceptionResponse = exception.getResponse() as any;

      // <PERSON><PERSON>m tra xem có phải lỗi validation không
      if (exceptionResponse.message === 'Validation failed' && exceptionResponse.errors) {
        const errorResponse = {
          statusCode: status,
          message: exceptionResponse.message,
          errors: exceptionResponse.errors,
          path: request.url,
          timestamp: new Date().toISOString(),
          data: exception instanceof HttpException ? exception.getResponse()['data'] : null,
        };

        // Trả về lỗi validation chi tiết
        return response.status(status).send(errorResponse);
      }
    }

    // Xử lý các lỗi HttpException khác
    if (exception instanceof HttpException) {
      const exceptionResponse = exception.getResponse() as any;

      // Tạo response với thông tin cơ bản, không hiển thị chi tiết lỗi
      const errorResponse = {
        statusCode: status,
        errorCode: exceptionResponse.code || status,
        message: exception.message,
        path: request.url,
        timestamp: new Date().toISOString(),
        data: exception instanceof HttpException ? exception.getResponse()['data'] : null,
      };

      return response.status(status).send(errorResponse);
    }


    const errorResponse = {
      statusCode: status,
      errorCode: status,
      message: 'Hệ thống đang bận, vui lòng thử lại sau.',
      path: request.url,
      timestamp: new Date().toISOString(),
      data: exception instanceof HttpException ? exception.getResponse()['data'] : null,
    };
    // Log chi tiết lỗi để debug nhưng không hiển thị trong response
    this.logError(exception, request);

    response.status(status).send(errorResponse);
  }

  private logError(exception: any, request: any): void {
    const status =
      exception instanceof HttpException
        ? exception.getStatus()
        : HttpStatus.INTERNAL_SERVER_ERROR;

    // Log chi tiết lỗi
    this.logger.error(
      `[${request.method}] ${request.url} - Status: ${status}`,
      {
        exception: {
          name: exception.name,
          message: exception.message,
          stack: exception.stack,
        },
        request: {
          headers: request.headers,
          query: request.query,
          body: request.body,
          params: request.params,
        },
        timestamp: new Date().toISOString(),
      },
    );
  }
}
