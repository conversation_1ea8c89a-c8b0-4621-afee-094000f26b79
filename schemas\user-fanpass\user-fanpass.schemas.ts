import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { BaseDocument, BaseSchema, addSoftDeleteHooks, addUpdateHooks, defaultSchemaOptions } from '../index';
import { IUserFanpassPaymentInfo, IUserFanpassStatus } from './user-fanpass.interfaces';

export type UserFanpassDocument = HydratedDocument<UserFanpass> & BaseDocument;
@Schema({
    collection: 'user_fanpass',
    ...defaultSchemaOptions,
})
export class UserFanpass extends BaseSchema {
    @Prop()
    fanpassId: string;

    @Prop()
    refId: string;

    @Prop({
        type: String,
        required: true,
        index: true,
    })
    orderId: string;

    @Prop({
        type: String,
        required: true,
        index: true,
    })
    ticketId: string;

    @Prop({
        type: String,
        required: true,
    })
    ticketType: string;

    @Prop({})
    ticketDescription: string;

    @Prop({})
    ticketPrice: number;

    @Prop({
        type: String,
        required: true,
        index: true,
    })
    userId: string;

    @Prop({
        enum: Object.values(IUserFanpassStatus),
        default: IUserFanpassStatus.ACTIVE,
    })
    status: string;

    @Prop({
        type: Date,
        index: true
    })
    paymentTime: Date;

    @Prop()
    paymentGateway: string;

    @Prop({
        type: {
            name: { type: String },
            email: { type: String },
            phoneNumber: { type: String },
            address: { type: String },
        },
        required: true,
        default: {},
        _id: false,
    })
    paymentInfo: IUserFanpassPaymentInfo;

    @Prop({})
    discountPercent: number;

    @Prop({})
    totalAmount: number;
}

export const UserFanpassSchema = SchemaFactory.createForClass(UserFanpass);
addUpdateHooks(UserFanpassSchema);
addSoftDeleteHooks(UserFanpassSchema);
