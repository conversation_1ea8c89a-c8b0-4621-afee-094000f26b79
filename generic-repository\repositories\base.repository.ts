import { Document, FilterQuery, Model, UpdateQuery, QueryOptions, PipelineStage, UpdateWriteOpResult, Connection, ClientSession } from 'mongoose';
import { NotFoundException } from '@nestjs/common';
import { IBaseRepository } from './base.repository.interface';
import { UpdateOptions } from 'mongodb';
import { InjectConnection } from '@nestjs/mongoose';

export class BaseRepository<T extends Document> implements IBaseRepository<T> {
  constructor(
    protected readonly model: Model<T>,
    @InjectConnection() protected readonly connection?: Connection

  ) { }

  /**
   * <PERSON><PERSON> dụng explain để phân tích truy vấn
   */

  /**
  * Thực hiện một loạt các thao tác trong một transaction
  * @param callback Hàm chứa các thao tác cần thực hiện trong transaction
  * @param options Tùy chọn cho transaction
  */
  async withTransaction<TResult>(
    callback: (session: ClientSession) => Promise<TResult>,
    options?: any
  ): Promise<TResult> {
    if (!this.connection) {
      throw new Error('Connection is not available for transactions');
    }

    const session = await this.connection.startSession();
    let result: TResult;

    try {
      // Bắt đầu transaction
      await session.withTransaction(async () => {
        // Thực hiện callback với session
        result = await callback(session);
      }, options);
      return result;
    } finally {
      // Kết thúc session bất kể kết quả
      await session.endSession();
    }
  }


  async explain(
    filterQuery: FilterQuery<T>,
    projection?: Record<string, unknown>,
    options?: QueryOptions,
    verbosity: 'queryPlanner' | 'executionStats' | 'allPlansExecution' = 'executionStats'
  ): Promise<any> {
    return this.model.find(filterQuery, projection, options).explain(verbosity);
  }


  /**
   * Tìm một document theo điều kiện
   */
  async findOne(
    filterQuery: FilterQuery<T>,
    projection?: Record<string, unknown>,
    options?: QueryOptions,
  ): Promise<T | null> {
    return this.model.findOne(filterQuery, projection, options).exec();
  }

  /**
   * Tìm một document theo điều kiện hoặc throw exception nếu không tìm thấy
   */
  async findOneOrFail(
    filterQuery: FilterQuery<T>,
    projection?: Record<string, unknown>,
    options?: QueryOptions,
  ): Promise<T> {
    const document = await this.findOne(filterQuery, projection, options);
    if (!document) {
      throw new NotFoundException(`${this.model.modelName} not found`);
    }
    return document;
  }

  /**
   * Tìm nhiều documents theo điều kiện
   */
  async find(
    filterQuery: FilterQuery<T>,
    projection?: Record<string, unknown>,
    options?: QueryOptions,
  ): Promise<T[]> {
    return this.model.find(filterQuery, projection, options).exec();
  }

  /**
   * Tạo một document mới
   */
  async create(createDto: Partial<T>): Promise<T> {
    const newDocument = new this.model(createDto);
    return newDocument.save();
  }

  /**
   * Tạo nhiều documents
   */
  async insertMany(createDtos: Partial<T>[]): Promise<T[]> {
    return this.model.insertMany(createDtos) as unknown as T[];
  }

  /**
   * Cập nhật một document theo điều kiện
   */
  async findOneAndUpdate(
    filterQuery: FilterQuery<T>,
    updateQuery: UpdateQuery<T>,
    options: QueryOptions = { new: true },
  ): Promise<T | null> {
    return this.model.findOneAndUpdate(filterQuery, updateQuery, options).exec();
  }

  /**
   * Cập nhật một document theo điều kiện hoặc throw exception nếu không tìm thấy
   */
  async findOneAndUpdateOrFail(
    filterQuery: FilterQuery<T>,
    updateQuery: UpdateQuery<T>,
    options: QueryOptions = { new: true },
  ): Promise<T> {
    const document = await this.findOneAndUpdate(filterQuery, updateQuery, options);
    if (!document) {
      throw new NotFoundException(`${this.model.modelName} not found`);
    }
    return document;
  }


  async updateOne(
    filterQuery: FilterQuery<T>,
    updateQuery: UpdateQuery<T>,
    options?: any,
  ): Promise<UpdateWriteOpResult> {
    return await this.model.updateOne(filterQuery, updateQuery, {
      ...options,
      new: true, // Trả về document mới sau khi cập nhật
    }).exec();
  }

  /**
   * Cập nhật nhiều documents theo điều kiện
   */
  async updateMany(
    filterQuery: FilterQuery<T>,
    updateQuery: UpdateQuery<T>,
  ): Promise<{ matchedCount: number; modifiedCount: number }> {
    const result = await this.model.updateMany(filterQuery, updateQuery).exec();
    return {
      matchedCount: result.matchedCount,
      modifiedCount: result.modifiedCount,
    };
  }

  /**
   * Xóa một document theo điều kiện
   */
  async findOneAndDelete(
    filterQuery: FilterQuery<T>,
    options?: QueryOptions,
  ): Promise<T | null> {
    return this.model.findOneAndDelete(filterQuery, options).exec();
  }

  /**
   * Xóa một document theo điều kiện hoặc throw exception nếu không tìm thấy
   */
  async findOneAndDeleteOrFail(
    filterQuery: FilterQuery<T>,
    options?: QueryOptions,
  ): Promise<T> {
    const document = await this.findOneAndDelete(filterQuery, options);
    if (!document) {
      throw new NotFoundException(`${this.model.modelName} not found`);
    }
    return document;
  }

  /**
   * Xóa nhiều documents theo điều kiện
   */
  async deleteMany(filterQuery: FilterQuery<T>): Promise<{ deletedCount: number }> {
    const result = await this.model.deleteMany(filterQuery).exec();
    return { deletedCount: result.deletedCount || 0 };
  }

  /**
   * Đếm số lượng documents theo điều kiện
   */
  async count(filterQuery: FilterQuery<T>): Promise<number> {
    return this.model.countDocuments(filterQuery).exec();
  }

  /**
   * Kiểm tra document có tồn tại không
   */
  async exists(filterQuery: FilterQuery<T>): Promise<boolean> {
    const count = await this.count(filterQuery);
    return count > 0;
  }

  /**
   * Thực hiện aggregation pipeline
   */
  async aggregate(pipeline: PipelineStage[]): Promise<any[]> {
    return this.model.aggregate(pipeline).exec();
  }

  /**
   * Phân trang
   */
  async pagination(
    filterQuery: FilterQuery<T>,
    options: {
      page?: number;
      limit?: number;
      sort?: Record<string, 1 | -1>;
      projection?: Record<string, unknown>;
    } = {},
  ): Promise<{
    data: T[];
    pagination: {
      total: number;
      page: number;
      limit: number;
      pages: number;
    };
  }> {
    const page = options.page || 1;
    const limit = options.limit || 10;
    const skip = (page - 1) * limit;

    const [data, total] = await Promise.all([
      this.model
        .find(filterQuery, options.projection)
        .sort(options.sort)
        .skip(skip)
        .limit(limit)
        .exec(),
      this.count(filterQuery),
    ]);

    return {
      data,
      pagination: {
        total,
        page,
        limit,
        pages: Math.ceil(total / limit),
      },
    };
  }
}
