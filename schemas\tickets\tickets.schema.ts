import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { BaseDocument, BaseSchema, addSoftDeleteHooks, addUpdateHooks, defaultSchemaOptions } from '../index';

export type TicketsDocument = HydratedDocument<Tickets> & BaseDocument;

@Schema({
  collection: 'tickets',
  ...defaultSchemaOptions,
})
export class Tickets extends BaseSchema {

  @Prop({
    type: String,
    required: true,
    index: true,
  })
  calendarId: string;

  @Prop({
    type: String,
    required: true,
    index: true,
  })
  eventId: string;

  //chưa định nghĩa trong bảng tickets
  @Prop({
    type: String,
  })
  eventName: string;

  @Prop({
    type: String,
    required: true,
    index: true,
  })
  rowId: string;

  @Prop({
    type: String,
    required: true,
    index: true,
  })
  zoneId: string;

  @Prop({
    type: String,
    required: false,
    index: true,
  })
  code: string;

  @Prop({
    type: String,
    required: true,
    default: 'idle',
    index: true,
  })
  status: string;

  @Prop({
    type: Boolean,
    required: false,
    default: false,
  })
  checkin: boolean;

  @Prop({
    type: String,
    required: false,
  })
  customerName: string;

  @Prop({
    type: String,
    required: false,
    index: true,
  })
  customerEmail: string;

  @Prop({
    type: String,
    required: false,
  })
  customerPhone: string;

  @Prop({
    type: String,
    required: false,
  })
  customerNote: string;

  @Prop({
    type: String,
    required: false,
  })
  customerExtraData: string;

  @Prop({
    type: String,
    required: false,
  })
  orderId: string;

  @Prop({
    type: String,
    required: true,
    index: true,
  })
  ticketClassId: string;

  @Prop({
    type: Number,
    rangeKey: true, // Sort Key
  })
  position: number;

  @Prop({
    type: Number,
    default: 1,
  })
  zonePosition: number;

  @Prop({
    type: Number,
    default: 1,
  })
  rowPosition: number;

  @Prop({
    type: String,
  })
  generateCode: string;

  @Prop({
    type: Date,
    required: false,
  })
  holdAt: Date;

  @Prop({
    type: Date,
    required: false,
  })
  soldAt: Date;

  @Prop({
    type: String,
    required: false,
    default: ''
  })
  paymentGateway: string;

  @Prop()
  checkedInBy: string;

  @Prop()
  checkinTime: Date;


}

export const TicketsSchema = SchemaFactory.createForClass(Tickets);
addUpdateHooks(TicketsSchema);
addSoftDeleteHooks(TicketsSchema);

