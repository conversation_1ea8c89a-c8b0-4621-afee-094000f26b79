import { Schema, SchemaOptions } from 'mongoose';

export const defaultSchemaOptions: SchemaOptions = {
  timestamps: { createdAt: 'createdAt', updatedAt: 'updatedAt' },
  toJSON: {
    virtuals: true,
    transform: (_, ret) => {
      delete ret.__v;
      ret._id = ret._id.toString();
      return ret;
    },
  },
  toObject: {
    virtuals: true,
    transform: (_, ret) => {
      delete ret.__v;
      ret._id = ret._id.toString();
      return ret;
    },
  },
};

export const addSoftDeleteHooks = (schema: Schema): void => {
  // Thêm middleware để không trả về các document đã bị xóa mềm
  schema.pre('find', function () {
    if (!this.getQuery().includeDeleted) {
      this.where({ isDeleted: { $ne: true } });
    }
    delete this.getQuery().includeDeleted;
  });

  schema.pre('findOne', function () {
    if (!this.getQuery().includeDeleted) {
      this.where({ isDeleted: { $ne: true } });
    }
    delete this.getQuery().includeDeleted;
  });

  schema.pre('countDocuments', function () {
    if (!this.getQuery().includeDeleted) {
      this.where({ isDeleted: { $ne: true } });
    }
    delete this.getQuery().includeDeleted;
  });

  // Thêm method để thực hiện xóa mềm
  schema.methods.softDelete = async function () {
    this.isDeleted = true;
    this.updatedAt = new Date();
    return this.save();
  };

  // Thêm method để khôi phục document đã xóa mềm
  schema.methods.restore = async function () {
    this.isDeleted = false;
    return this.save();
  };

  // Thêm static method để thực hiện xóa mềm nhiều document
  schema.statics.softDeleteMany = async function (filter = {}) {
    return this.updateMany(
      filter,
      {
        $set: {
          isDeleted: true,
          updatedAt: new Date(),
        },
      },
    );
  };

  // Thêm static method để khôi phục nhiều document đã xóa mềm
  schema.statics.restoreMany = async function (filter = {}) {
    return this.updateMany(
      filter,
      {
        $set: {
          isDeleted: false,
        },
      },
    );
  };
};

export const addUpdateHooks = (schema: Schema): void => {
  // Cập nhật updatedAt khi document được cập nhật
  schema.pre('save', function (next) {
    if (this.isModified()) {
      this.updatedAt = new Date();
    }
    next();
  });

  schema.pre('updateOne', function () {
    this.set({ updatedAt: new Date() });
  });

  schema.pre('updateMany', function () {
    this.set({ updatedAt: new Date() });
  });

  schema.pre('findOneAndUpdate', function () {
    this.set({ updatedAt: new Date() });
  });
};