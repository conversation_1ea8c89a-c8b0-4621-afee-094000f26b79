import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { HydratedDocument } from 'mongoose';
import { BaseDocument, BaseSchema, addSoftDeleteHooks, addUpdateHooks, defaultSchemaOptions } from '../index';

export type EventTicketDocument = HydratedDocument<EventTicket> & BaseDocument;

@Schema({
    collection: 'event_tickets',
    ...defaultSchemaOptions,
})
export class EventTicket extends BaseSchema {
    @Prop({
        required: true,
        index: true,
        type: String,
    })
    eventId: string;

    @Prop({
        type: String,
        default: '',
    })
    refId: string;

    @Prop({
        type: String,
        default: '',
    })
    orderId: string;

    @Prop({
        type: String,
        required: true,
        index: true,
    })
    ticketId: string;

    @Prop({
        type: String,
        required: true,
    })
    ticketType: string;

    @Prop({
        type: String,
        default: '',
    })
    ticketDescription: string

    @Prop({
        type: Number,
        default: 0,
    })
    ticketPrice: number;

    @Prop({
        type: Number,
        default: 0,
    })
    ticketPriceInUSD: number;

    @Prop({
        type: Number,
        default: 0,
    })
    discountPercent: number;

    @Prop({
        type: Number,
        default: 0,
    })
    totalAmount: number;

    @Prop({
        type: String,
        required: true,
        index: true,
    })
    userId: string;

    @Prop({
        type: Boolean,
        default: false,
    })
    checkedIn: boolean;

    @Prop({
        type: String,
        default: '',
    })
    checkedInBy: string;

    @Prop({
        type: Date,
    })
    checkinTime: Date;

    @Prop({
        type: Date,
    })
    paymentTime: Date;

    @Prop({
        type: String,
        default: '',
    })
    paymentGateway: string;


    @Prop({ type: Object, default: {} })
    paymentInfo: Record<string, any>;
    // @Prop({
    //     type: {
    //         name: String,
    //         email: String,
    //         phoneNumber: String,
    //     },
    //     default: {},
    //     _id: false,
    // })
    // paymentInfo: {
    //     name: string;
    //     email: string;
    //     phoneNumber: string;
    // }

    @Prop({
        type: Object,
        default: {},
        _id: false,
    })
    deliveryInfo: Record<string, any>[];

    // @Prop({
    //     type: {
    //         seatCode: String,
    //         qrText: String,
    //         qrUrl: String,
    //     },
    //     default: {},
    //     _id: false,

    // })
    // deliveryInfo: {
    //     seatCode: string,
    //     qrText: string,
    //     qrUrl: string,
    // }

    calendarId: string;

    @Prop({
        type: Object,
        default: {},
        _id: false,
    })
    promotionApplied: Record<string, any>[];

}

export const EventTicketSchema = SchemaFactory.createForClass(EventTicket);

addSoftDeleteHooks(EventTicketSchema);
addUpdateHooks(EventTicketSchema);
