Cấu trúc ý nghĩa file 
        src/
        ├── auth/                      <-- <PERSON><PERSON><PERSON> thực
        ├── cluster/                  <-- Tùy xử lý cluster (xử lý tránh tắc nghẽn request là 1 cpu đa nhân)
        ├── database/                 <-- Kết nối MongoDB (quan trọng)
        ├── decorators/               <-- 
        ├── exception/                <-- Xử lý lỗi toàn cục
        ├── generic-repository/       <-- Tái sử dụng truy vấn DB
        ├── logger/                   <-- Log hệ thống
        ├── pipes/                    <-- Validate dữ liệu
        ├── response/                 <-- Chuẩn hóa phản hồi
        ├── schemas/                  <-- Chứa tất cả mongoose schema
        ├── shared.module.ts         <-- Gom các module dùng chung
        └── index.ts                 <-- Khởi động app